from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import crud, schemas, services, models
from database import get_db, engine, Base

router = APIRouter(
    prefix="/scans",
    tags=["Scans"],
    responses={404: {"description": "Not found"}},
)

@router.on_event("startup")
async def startup_event():
    Base.metadata.create_all(bind=engine)
    # Initialize default parameters if they don't exist
    db = next(get_db())
    try:
        crud.initialize_default_parameters(db)
    finally:
        db.close()

@router.post("/upload", response_model=schemas.Scan)
async def upload_scan_file(
    scan_name: str,
    is_baseline: Optional[bool] = False,
    kvk_id: Optional[int] = None,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    Uploads a scan file (CSV or Excel) and processes its data.
    - **scan_name**: Name for this scan (e.g., "Pre-KVK", "Scan 1").
    - **is_baseline**: Mark this scan as the baseline for comparisons.
    - **file**: The scan data file.
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided.")

    contents = await file.read()
    try:
        parsed_data = services.parse_player_data_file(file_content=contents, filename=file.filename)
    except HTTPException as e: # Catch parsing errors from service layer
        raise e
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing file content: {str(e)}")

    if not parsed_data:
        raise HTTPException(status_code=400, detail="No data could be parsed from the file.")

    scan_upload_data = schemas.ScanUpload(
        scan_name=scan_name,
        is_baseline=is_baseline,
        kvk_id=kvk_id,
        data=parsed_data
    )

    try:
        db_scan = services.process_scan_data(db=db, scan_upload=scan_upload_data)
        return db_scan
    except HTTPException as e: # Catch processing errors from service layer (e.g. duplicate scan name)
        raise e
    except Exception as e:
        # Log the full error for debugging
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred while processing the scan: {str(e)}")

@router.get("/", response_model=List[schemas.Scan])
def read_scans(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    Retrieve a list of all scans, ordered by most recent first.
    """
    scans = crud.get_scans(db, skip=skip, limit=limit)
    return scans

@router.get("/{scan_id}", response_model=schemas.Scan)
def read_scan(
    scan_id: int,
    db: Session = Depends(get_db)
):
    """
    Retrieve a specific scan by its ID.
    """
    db_scan = crud.get_scan(db, scan_id=scan_id)
    if db_scan is None:
        raise HTTPException(status_code=404, detail="Scan not found")
    return db_scan

@router.get("/{scan_id}/stats", response_model=List[schemas.PlayerStatWithScanInfo])
def read_stats_for_scan(
    scan_id: int,
    db: Session = Depends(get_db)
):
    """
    Retrieve all player statistics associated with a specific scan.
    """
    db_scan = crud.get_scan(db, scan_id=scan_id)
    if db_scan is None:
        raise HTTPException(status_code=404, detail="Scan not found")

    # Fetch stats and eager load player and scan details for the response model
    stats = db.query(models.PlayerStat).filter(models.PlayerStat.scan_id == scan_id).all()
    # The PlayerStatWithScanInfo schema expects nested player and scan objects.
    # SQLAlchemy relationships should handle this if configured correctly in models and schemas.
    return stats

@router.post("/{scan_id}/recalculate_deltas", response_model=schemas.Scan)
def recalculate_deltas_for_scan(
    scan_id: int,
    baseline_scan_id: Optional[int] = None, # Explicitly specify baseline, or use system default
    db: Session = Depends(get_db)
):
    """
    Triggers a recalculation of delta statistics for a given end_scan_id against a baseline_scan_id.
    If baseline_scan_id is not provided, the system's default baseline scan will be used.
    """
    end_scan = crud.get_scan(db, scan_id=scan_id)
    if not end_scan:
        raise HTTPException(status_code=404, detail=f"End scan with ID {scan_id} not found.")

    if baseline_scan_id:
        start_scan = crud.get_scan(db, scan_id=baseline_scan_id)
        if not start_scan:
            raise HTTPException(status_code=404, detail=f"Specified baseline scan with ID {baseline_scan_id} not found.")
    else:
        start_scan = crud.get_baseline_scan(db)
        if not start_scan:
            raise HTTPException(status_code=400, detail="No default baseline scan found. Please set a baseline scan or provide a baseline_scan_id.")

    if start_scan.id == end_scan.id:
        raise HTTPException(status_code=400, detail="Cannot calculate deltas for a scan against itself.")

    try:
        services.calculate_delta_stats_for_scan_pair(db, start_scan_id=start_scan.id, end_scan_id=end_scan.id)
        # Return the scan for which deltas were recalculated, or a success message
        return end_scan
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error recalculating deltas: {str(e)}")

@router.post("/set_baseline/{scan_id}", response_model=schemas.Scan)
def set_baseline_scan_endpoint(
    scan_id: int,
    db: Session = Depends(get_db)
):
    """
    Sets a specific scan as the baseline scan. Any previous baseline will be unset.
    """
    scan_to_set = crud.get_scan(db, scan_id=scan_id)
    if not scan_to_set:
        raise HTTPException(status_code=404, detail=f"Scan with ID {scan_id} not found.")

    # Unset other baseline scans
    other_baseline_scans = db.query(models.Scan).filter(models.Scan.is_baseline == True, models.Scan.id != scan_id).all()
    for obs in other_baseline_scans:
        obs.is_baseline = False

    scan_to_set.is_baseline = True
    db.commit()
    db.refresh(scan_to_set)
    return scan_to_set