import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { fetchScans } from '../services/scanService'; // Assuming fetchScans returns Scan[]
import { getKvKList } from '../services/kvkService'; // Added: Assuming getKvKList returns KvKData[]
import { ScanData, KvKData } from '../types/dataTypes'; // Added KvKData, ensured Scan type is available
import { useTheme } from '../contexts/ThemeContext'; // Theme context no longer needed for direct styling

const ScansListPage: React.FC = () => {
  // const { theme } = useTheme(); 
  
  const {
    data: scans = [],
    isLoading: isLoadingScans,
    error: errorScans,
    refetch: refetchScans
  } = useQuery<ScanData[], Error>({
    queryKey: ['scansList'],
    queryFn: fetchScans,
    retry: 1,
    refetchOnWindowFocus: false
  });
  
  const {
    data: kvkList = [],
    isLoading: isLoadingKvK,
    error: errorKvK,
    refetch: refetchKvKs
  } = useQuery<KvKData[], Error>({
    queryKey: ['kvkList'],
    queryFn: getKvKList,
    retry: 1,
    refetchOnWindowFocus: false
  });
  
  if (isLoadingScans || isLoadingKvK) return (
    <div className="text-center py-10 text-gray-700 dark:text-gray-300">
      <div className="animate-pulse">Loading data...</div>
    </div>
  );
  
  if (errorScans || errorKvK) {
    const combinedError = errorScans || errorKvK;
    const refetchAll = () => {
      if (errorScans) refetchScans();
      if (errorKvK) refetchKvKs();
    };
    return (
      <div className="text-center py-10 bg-red-50 text-red-600 rounded-lg p-4 max-w-2xl mx-auto border border-red-200">
        <p className="font-semibold">Error fetching data:</p>
        <p>{(combinedError as Error).message}</p>
        <button
          onClick={refetchAll}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  // Process and group scans
  const scansByKvkId: Record<string, ScanData[]> = {};
  const unassignedScans: ScanData[] = [];
  const kvkMap = new Map(kvkList.map(kvk => [kvk.id, kvk]));
  
  scans.forEach(scan => {
    // Assuming Scan interface has kvkId?: string | null
    if (scan.kvkId && kvkMap.has(scan.kvkId)) {
      if (!scansByKvkId[scan.kvkId]) {
        scansByKvkId[scan.kvkId] = [];
      }
      scansByKvkId[scan.kvkId].push(scan);
    } else {
      unassignedScans.push(scan);
    }
  });
  
  const sortedKvKList = [...kvkList].sort((a, b) => (b.season || 0) - (a.season || 0) || a.name.localeCompare(b.name));
  
  const noDataAvailable = sortedKvKList.length === 0 && unassignedScans.length === 0;
  
  return (
    <div className="max-w-6xl mx-auto p-4">
      <header className="mb-8 flex flex-col sm:flex-row justify-between items-center">
        <h1 className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-4 sm:mb-0">
          Uploaded Scans by KvK
        </h1>
        <Link
          to="/upload-scan"
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out flex items-center justify-center"
        >
          Upload New Scan
        </Link>
      </header>
  
      {noDataAvailable && scans.length === 0 ? (
        <div className="text-center py-12 px-4 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <svg
            className="mx-auto h-16 w-16 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
            />
          </svg>
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">No scans found</h3>
          <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
            No scan data has been uploaded yet.
          </p>
          <div className="mt-6">
            <Link
              to="/upload-scan"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg
                className="-ml-1 mr-2 h-5 w-5"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
              Upload New Scan
            </Link>
          </div>
        </div>
      ) : noDataAvailable && scans.length > 0 ? (
        <div className="text-center py-10 text-gray-700 dark:text-gray-300">
            <p>No KvKs found, or existing scans are not yet associated with any KvK.</p>
        </div>
      ) : (
        <>
          {sortedKvKList.map(kvk => {
            const currentKvkScans = scansByKvkId[kvk.id] || [];
            return (
              <div key={kvk.id} className="mb-8 bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                <div className="p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-xl font-semibold text-blue-600 dark:text-blue-400">{kvk.name} (Season {kvk.season})</h2>
                </div>
                {currentKvkScans.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-900">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Scan Name</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">KvK Phase</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Baseline</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Players</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {currentKvkScans.map((scan) => (
                          <tr key={scan.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                            <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm font-medium text-gray-900 dark:text-white">{scan.name}</div></td>
                            <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-gray-500 dark:text-gray-300">{new Date(scan.date).toLocaleDateString()}</div></td>
                            <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-gray-500 dark:text-gray-300">{scan.kvkPhase}</div></td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${scan.isBaseline ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
                                {scan.isBaseline ? 'Yes' : 'No'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-gray-500 dark:text-gray-300">{scan.players?.length || 0}</div></td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <Link to={`/scans/${scan.id}`} className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">View Details</Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="p-4 text-gray-500 dark:text-gray-400">No scans found for this KvK.</p>
                )}
              </div>
            );
          })}
  
          {unassignedScans.length > 0 && (
            <div className="mb-8 bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
              <div className="p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300">Other Scans (Not tied to a KvK)</h2>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-900">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Scan Name</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">KvK Phase</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Baseline</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Players</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {unassignedScans.map((scan) => (
                      <tr key={scan.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                        <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm font-medium text-gray-900 dark:text-white">{scan.name}</div></td>
                        <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-gray-500 dark:text-gray-300">{new Date(scan.date).toLocaleDateString()}</div></td>
                        <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-gray-500 dark:text-gray-300">{scan.kvkPhase}</div></td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${scan.isBaseline ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
                            {scan.isBaseline ? 'Yes' : 'No'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-gray-500 dark:text-gray-300">{scan.players?.length || 0}</div></td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Link to={`/scans/${scan.id}`} className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">View Details</Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ScansListPage;