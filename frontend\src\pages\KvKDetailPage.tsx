import React, { useState, useMemo } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { useQuery } from '@tanstack/react-query';
import { getKvKById, getScans, getKvKPerformanceSummary } from '../api/api';
import { formatLargeNumber, formatDate, timeAgo } from '../utils/formatters';
import PlayerTable from '../components/PlayerTable';
import { PlayerScanData } from '../types/dataTypes';
import { FaChartLine, FaCrosshairs, FaShieldAlt, FaSkullCrossbones, FaInfoCircle, FaCalendarAlt, FaUsers, FaListOl, FaExclamationCircle, FaUpload } from 'react-icons/fa';

// Define tabs for the KvK detail page
type TabType = 'overview' | 'killpoints' | 'deads' | 't45kills';

const KvKDetailPage: React.FC = () => {
  const { kvkId } = useParams<{ kvkId: string }>();
  const { theme } = useTheme();
  const { canUploadScans } = useUser();
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  // Fetch KvK data
  const { data: kvk, isLoading: isLoadingKvK } = useQuery({
    queryKey: ['kvk', kvkId],
    queryFn: () => getKvKById(kvkId || ''),
    enabled: !!kvkId
  });

  // Fetch all scans
  const { data: allScans = [], isLoading: isLoadingScans } = useQuery({
    queryKey: ['scansList'],
    queryFn: getScans
  });

  // Fetch KvK performance summary with real data
  const { data: performanceData, isLoading: isLoadingPerformance } = useQuery({
    queryKey: ['kvkPerformance', kvkId],
    queryFn: () => getKvKPerformanceSummary(kvkId || '', 100),
    enabled: !!kvkId
  });

  // Filter scans for this KvK
  const kvkScans = useMemo(() => {
    return allScans.filter(scan => scan.kvkId === kvkId);
  }, [allScans, kvkId]);

  // Sort scans by date (newest first)
  const sortedScans = useMemo(() => {
    return [...kvkScans].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [kvkScans]);

  // Get latest scan
  const latestScan = sortedScans[0];

  // Get baseline scan
  const baselineScan = kvkScans.find(scan => scan.isBaseline);

  // Use real performance data from API
  const playerGains = useMemo(() => {
    if (!performanceData?.performance_data) return [];
    
    // Transform API data to match component expectations
    return performanceData.performance_data.map((player: any) => ({
      id: player.player_id,
      name: player.name,
      governorId: player.governor_id,
      alliance: player.alliance,
      powerGain: player.power_delta,
      killPointsGain: player.kp_delta,
      deadsGain: player.dead_delta,
      t4KillsGain: player.t4_kills_delta,
      t5KillsGain: player.t5_kills_delta,
      t45KillsGain: (player.t4_kills_delta || 0) + (player.t5_kills_delta || 0),
      kpPerDead: player.kp_per_dead,
      efficiencyScore: player.efficiency_score,
      isTopPerformer: player.is_top_performer,
      needsImprovement: player.needs_improvement,
      isNew: player.power_delta === player.power // If power delta equals total power, likely new player
    }));
  }, [performanceData]);

  // Use summary stats from API or calculate from player data
  const { totalKillPointsGain, totalT5Casualties, totalDeadsGain, totalPlayers, avgKpPerDead } = useMemo(() => {
    if (performanceData?.summary_stats) {
      return {
        totalKillPointsGain: performanceData.summary_stats.total_kp_gained,
        totalT5Casualties: playerGains.reduce((acc, player) => acc + (player.t5KillsGain || 0), 0),
        totalDeadsGain: performanceData.summary_stats.total_dead_lost,
        totalPlayers: performanceData.summary_stats.total_players,
        avgKpPerDead: performanceData.summary_stats.avg_kp_per_dead
      };
    }
    
    // Fallback calculation if API data not available
    return playerGains.reduce((acc, player) => ({
      totalKillPointsGain: acc.totalKillPointsGain + player.killPointsGain,
      totalT5Casualties: acc.totalT5Casualties + (player.t5KillsGain || 0),
      totalDeadsGain: acc.totalDeadsGain + player.deadsGain,
      totalPlayers: playerGains.length,
      avgKpPerDead: 0 // Will be calculated below
    }), {
      totalKillPointsGain: 0,
      totalT5Casualties: 0,
      totalDeadsGain: 0,
      totalPlayers: 0,
      avgKpPerDead: 0
    });
  }, [playerGains, performanceData]);

  // Sort players by the active tab metric
  const sortedPlayers = useMemo(() => {
    if (activeTab === 'overview') {
      return [...playerGains].sort((a, b) => b.killPointsGain - a.killPointsGain);
    } else if (activeTab === 'killpoints') {
      return [...playerGains].sort((a, b) => b.killPointsGain - a.killPointsGain);
    } else if (activeTab === 'deads') {
      return [...playerGains].sort((a, b) => b.deadsGain - a.deadsGain);
    } else if (activeTab === 't45kills') {
      return [...playerGains].sort((a, b) => b.t45KillsGain - a.t45KillsGain);
    }
    return playerGains;
  }, [playerGains, activeTab]);

  // Loading state
  if (isLoadingKvK || isLoadingScans || isLoadingPerformance) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`flex flex-col items-center justify-center min-h-[calc(100vh-200px)] ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
          <p className="text-lg">Loading KvK performance data...</p>
          {performanceData && (
            <p className="text-sm mt-2">Analyzing {performanceData.summary_stats?.total_players || 0} players...</p>
          )}
        </div>
      </div>
    );
  }

  // Error state
  if (!kvk) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`text-center ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>
          KvK not found. Please check the URL and try again.
        </div>
        <div className="mt-4 text-center">
          <Link
            to="/"
            className={`inline-block px-4 py-2 rounded-md ${theme === 'light'
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-blue-700 text-white hover:bg-blue-600'}`}
          >
            Return to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={`container mx-auto px-4 py-8 transition-colors duration-300 ${theme === 'light' ? 'bg-slate-100' : 'bg-gray-900'}`}>
      {/* KvK Header */}
      <div className="mb-8 p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className={`text-3xl font-bold ${theme === 'light' ? 'text-indigo-700' : 'text-indigo-400'}`}>
              {kvk.name} (Season {kvk.season})
            </h1>
            <p className={`mt-2 text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              Status: <span className={`font-semibold px-2 py-1 rounded-full text-xs ${kvk.status === 'active' ? (theme === 'light' ? 'bg-green-100 text-green-700' : 'bg-green-700 text-green-100') : kvk.status === 'completed' ? (theme === 'light' ? 'bg-gray-100 text-gray-700' : 'bg-gray-700 text-gray-200') : (theme === 'light' ? 'bg-yellow-100 text-yellow-700' : 'bg-yellow-700 text-yellow-100')}`}>{kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}</span>
            </p>
          </div>
          {canUploadScans() && kvk.status !== 'completed' && (
            <div className="mt-4 md:mt-0">
              <Link
                to={`/upload?kvkId=${kvkId}`}
                className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium shadow-sm transition-colors ${theme === 'light'
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500'
                  : 'bg-indigo-500 text-white hover:bg-indigo-400 focus:ring-indigo-600'}`}
              >
                <FaChartLine className="-ml-1 mr-2 h-5 w-5" />
                Upload New Scan
              </Link>
            </div>
          )}
        </div>
      </div>

      {/* Summary Cards - Enhanced with Real Data */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaCrosshairs className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-red-500' : 'text-red-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Total Kill Points Gain</h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`}>{formatLargeNumber(totalKillPointsGain)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>~({formatLargeNumber(totalKillPointsGain, true)})</p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaShieldAlt className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-purple-500' : 'text-purple-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>T5 Casualties</h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-purple-600' : 'text-purple-400'}`}>{formatLargeNumber(totalT5Casualties)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Severely Wounded/Dead</p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaSkullCrossbones className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-yellow-500' : 'text-yellow-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Deads Gain</h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'}`}>{formatLargeNumber(totalDeadsGain)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Total dead troops</p>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white hover:shadow-xl' : 'bg-gray-800 hover:bg-gray-700'} transition-shadow duration-300`}>
          <div className="flex items-center mb-3">
            <FaChartLine className={`h-8 w-8 mr-4 ${theme === 'light' ? 'text-green-500' : 'text-green-400'}`} />
            <div>
              <h3 className={`text-sm font-medium uppercase tracking-wider ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Avg KP/Dead</h3>
              <p className={`text-3xl font-bold ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`}>{avgKpPerDead.toFixed(2)}</p>
              <p className={`text-xs ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>Efficiency Ratio</p>
            </div>
          </div>
        </div>
      </div>

      {/* Scan Info - Redesigned */}
      <div className={`mb-8 p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
        <div className="flex flex-col md:flex-row md:items-start md:justify-between">
          <div>
            <div className="flex items-center mb-2">
              <FaInfoCircle className={`h-5 w-5 mr-2 ${theme === 'light' ? 'text-blue-500' : 'text-blue-400'}`} />
              <h3 className={`text-lg font-semibold ${theme === 'light' ? 'text-blue-700' : 'text-blue-300'}`}>
                Scan Information
              </h3>
            </div>
            <p className={`mt-1 text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              <span className="font-medium">Analysis Period:</span> 
              {performanceData?.scan_period ? (
                <span>
                  {performanceData.scan_period.start_date ? formatDate(performanceData.scan_period.start_date) : 'N/A'} → {performanceData.scan_period.end_date ? formatDate(performanceData.scan_period.end_date) : 'N/A'}
                </span>
              ) : (
                <span>{baselineScan ? formatDate(baselineScan.date) : 'N/A'} → {latestScan ? formatDate(latestScan.date) : 'N/A'}</span>
              )}
            </p>
            <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              <span className="font-medium">Baseline Scan ID:</span> {performanceData?.baseline_scan || baselineScan?.id || 'N/A'}
            </p>
            <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
              <span className="font-medium">Latest Scan ID:</span> {performanceData?.latest_scan || latestScan?.id || 'N/A'}
            </p>
          </div>
          <div className="mt-4 md:mt-0 md:text-right">
            <p className={`text-sm flex items-center ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              <FaCalendarAlt className="h-4 w-4 mr-2 opacity-75" /> Last updated: {latestScan ? timeAgo(latestScan.date) : 'N/A'}
            </p>
            <p className={`text-sm flex items-center mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
              <FaUsers className="h-4 w-4 mr-2 opacity-75" /> Total Players: {totalPlayers || 0}
            </p>
            {performanceData?.summary_stats && (
              <>
                <p className={`text-sm flex items-center mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                  <FaExclamationCircle className="h-4 w-4 mr-2 opacity-75" /> Top Performers: {performanceData.summary_stats.top_performers}
                </p>
                <p className={`text-sm flex items-center mt-1 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                  <FaExclamationCircle className="h-4 w-4 mr-2 opacity-75" /> Need Improvement: {performanceData.summary_stats.needs_improvement}
                </p>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Tabs - Enhanced Styling */}
      <div className="mb-6">
        <nav className={`flex space-x-1 rounded-lg p-1 ${theme === 'light' ? 'bg-gray-200' : 'bg-gray-700'}`}>
          {(['overview', 'killpoints', 'deads', 't45kills'] as TabType[]).map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`w-full py-2.5 px-1 text-sm font-medium rounded-md transition-colors focus:outline-none ${activeTab === tab
                  ? `${theme === 'light' ? 'bg-white text-indigo-700 shadow' : 'bg-gray-900 text-indigo-400 shadow-md'}`
                  : `${theme === 'light' ? 'text-gray-600 hover:bg-gray-50 hover:text-gray-800' : 'text-gray-300 hover:bg-gray-600 hover:text-gray-100'}`
                }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1).replace('killpoints', 'Kill Points').replace('t45kills', 'T4/T5 Kills')}
            </button>
          ))}
        </nav>
      </div>

      {/* Player Table Section - Wrapped for consistent styling */}
      <div className={`p-6 rounded-xl shadow-lg ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
        {activeTab === 'overview' && (
          <div>
            <h2 className={`text-xl font-semibold mb-4 flex items-center ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
              <FaListOl className="mr-3 h-5 w-5" /> Player Performance Overview
            </h2>
          </div>
        )}

        {activeTab === 'killpoints' && (
          <div>
            <h2 className={`text-xl font-semibold mb-4 flex items-center ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
              <FaCrosshairs className="mr-3 h-5 w-5" /> Kill Points Leaderboard
            </h2>
          </div>
        )}

        {activeTab === 'deads' && (
          <div>
            <h2 className={`text-xl font-semibold mb-4 flex items-center ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
              <FaSkullCrossbones className="mr-3 h-5 w-5" /> Deads Leaderboard
            </h2>
          </div>
        )}

        {activeTab === 't45kills' && (
          <div>
            <h2 className={`text-xl font-semibold mb-4 flex items-center ${theme === 'light' ? 'text-gray-800' : 'text-gray-100'}`}>
              <FaShieldAlt className="mr-3 h-5 w-5" /> T4-5 Kills Leaderboard
            </h2>
          </div>
        )}

        {/* Player Data Table or Informational Message */}
        {sortedPlayers.length > 0 ? (
          <PlayerTable players={sortedPlayers} />
        ) : (
          <div className={`text-center py-10 ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
            <FaExclamationCircle className="mx-auto h-12 w-12 text-yellow-400 mb-4" />
            <h3 className="text-xl font-semibold mb-3">
              {kvkScans.length === 0 ? "No Scans Available" : "Awaiting Full Data"}
            </h3>
            <p className="text-sm mb-6 max-w-md mx-auto">
              {kvkScans.length === 0
                ? "No scans have been uploaded for this KvK event yet. Upload a scan to begin tracking player statistics."
                : !baselineScan
                ? "A baseline scan is required to calculate player gains. Please upload a new scan and mark it as baseline, or mark an existing scan as baseline."
                : !latestScan 
                ? "No recent scan data is available for comparison."
                : "Player statistics will be shown once a baseline scan and at least one subsequent scan are available."}
            </p>
            {canUploadScans() && kvk && kvk.status !== 'completed' && (
              <Link
                to={`/upload?kvkId=${kvkId}${!baselineScan && kvkScans.length > 0 ? '&setBaseline=true' : ''}`}
                className={`inline-flex items-center px-6 py-3 rounded-lg text-sm font-medium shadow-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${theme === 'light'
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500'
                  : 'bg-indigo-500 text-white hover:bg-indigo-400 focus:ring-indigo-600'
                }`}
              >
                <FaUpload className="mr-2 h-5 w-5" />
                {kvkScans.length === 0 ? "Upload First Scan" : (!baselineScan ? "Upload/Set Baseline Scan" : "Upload New Scan")}
              </Link>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default KvKDetailPage;
