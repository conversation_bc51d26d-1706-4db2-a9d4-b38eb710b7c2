import React, { useState, useMemo, memo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { PlayerScanData } from '../types/dataTypes';
import { formatNumber } from '../utils/formatters';

interface PlayerTableProps {
  players: PlayerScanData[];
  title?: string;
  showAlliance?: boolean;
  pageSize?: number;
}

const PlayerTable: React.FC<PlayerTableProps> = ({
  players,
  title = 'Players',
  showAlliance = true,
  pageSize = 25
}) => {
  const { theme } = useTheme();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof PlayerScanData>('power');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Filter players based on search term
  const filteredPlayers = useMemo(() => {
    return players.filter(player => {
      const searchLower = searchTerm.toLowerCase();
      return (
        player.name.toLowerCase().includes(searchLower) ||
        player.governorId.toLowerCase().includes(searchLower) ||
        (player.alliance && player.alliance.toLowerCase().includes(searchLower))
      );
    });
  }, [players, searchTerm]);

  // Sort players
  const sortedPlayers = useMemo(() => {
    return [...filteredPlayers].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === undefined || bValue === undefined) return 0;

      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // Handle number comparison
      return sortDirection === 'asc'
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });
  }, [filteredPlayers, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedPlayers.length / pageSize);
  const paginatedPlayers = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedPlayers.slice(startIndex, startIndex + pageSize);
  }, [sortedPlayers, currentPage, pageSize]);

  // Handle sort
  const handleSort = (field: keyof PlayerScanData) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc'); // Default to descending for new sort field
    }
  };

  return (
    <div className={`rounded-lg overflow-hidden shadow-md ${theme === 'light' ? 'bg-white' : 'bg-gray-800'}`}>
      <div className={`px-4 py-3 ${theme === 'light' ? 'bg-blue-50' : 'bg-blue-900/30'} flex justify-between items-center`}>
        <h3 className={`font-semibold ${theme === 'light' ? 'text-blue-700' : 'text-blue-300'}`}>
          {title} ({filteredPlayers.length})
        </h3>
        <div className="flex items-center">
          <input
            type="text"
            placeholder="Search players..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1); // Reset to first page on search
            }}
            className={`px-3 py-1 text-sm rounded-md border ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-700'
                : 'border-gray-600 bg-gray-700 text-white'
            }`}
          />
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className={`${theme === 'light' ? 'bg-gray-50' : 'bg-gray-900/50'}`}>
            <tr>
              <th
                scope="col"
                className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider cursor-pointer`}
                onClick={() => handleSort('governorId')}
              >
                ID
                {sortField === 'governorId' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
              <th
                scope="col"
                className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider cursor-pointer`}
                onClick={() => handleSort('name')}
              >
                Name
                {sortField === 'name' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
              {showAlliance && (
                <th
                  scope="col"
                  className={`px-4 py-3 text-left text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider cursor-pointer`}
                  onClick={() => handleSort('alliance')}
                >
                  Alliance
                  {sortField === 'alliance' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </th>
              )}
              <th
                scope="col"
                className={`px-4 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider cursor-pointer`}
                onClick={() => handleSort('power')}
              >
                Power
                {sortField === 'power' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
              <th
                scope="col"
                className={`px-4 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider cursor-pointer`}
                onClick={() => handleSort('killPoints')}
              >
                Kill Points
                {sortField === 'killPoints' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
              <th
                scope="col"
                className={`px-4 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider cursor-pointer`}
                onClick={() => handleSort('deads')}
              >
                Deads
                {sortField === 'deads' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
              <th
                scope="col"
                className={`px-4 py-3 text-right text-xs font-medium ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'} uppercase tracking-wider cursor-pointer`}
                onClick={() => handleSort('t45Kills')}
              >
                T4-5 Kills
                {sortField === 't45Kills' && (
                  <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                )}
              </th>
            </tr>
          </thead>
          <tbody className={`${theme === 'light' ? 'bg-white' : 'bg-gray-800'} divide-y ${theme === 'light' ? 'divide-gray-200' : 'divide-gray-700'}`}>
            {paginatedPlayers.map((player) => (
              <tr key={player.governorId} className={`${theme === 'light' ? 'hover:bg-gray-50' : 'hover:bg-gray-700'}`}>
                <td className={`px-4 py-2 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                  {player.governorId}
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm font-medium ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {player.name}
                </td>
                {showAlliance && (
                  <td className={`px-4 py-2 whitespace-nowrap text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-300'}`}>
                    {player.alliance || '-'}
                  </td>
                )}
                <td className={`px-4 py-2 whitespace-nowrap text-sm text-right ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {formatNumber(player.power)}
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm text-right ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {formatNumber(player.killPoints)}
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm text-right ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {formatNumber(player.deads)}
                </td>
                <td className={`px-4 py-2 whitespace-nowrap text-sm text-right ${theme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                  {formatNumber(player.t45Kills)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className={`px-4 py-3 flex items-center justify-between border-t ${theme === 'light' ? 'border-gray-200' : 'border-gray-700'}`}>
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                currentPage === 1
                  ? `${theme === 'light' ? 'bg-gray-100 text-gray-400' : 'bg-gray-700 text-gray-500'}`
                  : `${theme === 'light' ? 'bg-white text-gray-700 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className={`ml-3 relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                currentPage === totalPages
                  ? `${theme === 'light' ? 'bg-gray-100 text-gray-400' : 'bg-gray-700 text-gray-500'}`
                  : `${theme === 'light' ? 'bg-white text-gray-700 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`
              }`}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className={`text-sm ${theme === 'light' ? 'text-gray-700' : 'text-gray-300'}`}>
                Showing <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, filteredPlayers.length)}
                </span>{' '}
                of <span className="font-medium">{filteredPlayers.length}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border text-sm font-medium ${
                    currentPage === 1
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">First</span>
                  &laquo;
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium ${
                    currentPage === 1
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">Previous</span>
                  &lsaquo;
                </button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === pageNum
                          ? `${theme === 'light' ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'z-10 bg-blue-900 border-blue-500 text-blue-300'}`
                          : `${theme === 'light' ? 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'}`
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium ${
                    currentPage === totalPages
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">Next</span>
                  &rsaquo;
                </button>
                <button
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border text-sm font-medium ${
                    currentPage === totalPages
                      ? `${theme === 'light' ? 'bg-gray-100 text-gray-400 border-gray-300' : 'bg-gray-700 text-gray-500 border-gray-600'}`
                      : `${theme === 'light' ? 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50' : 'bg-gray-800 text-gray-300 border-gray-600 hover:bg-gray-700'}`
                  }`}
                >
                  <span className="sr-only">Last</span>
                  &raquo;
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(PlayerTable);
